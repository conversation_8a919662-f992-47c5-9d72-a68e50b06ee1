package dev.pigmomo.yhkit2025.viewmodel

import android.app.Application
import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.repository.productmonitor.MonitoringPlanRepository
import dev.pigmomo.yhkit2025.data.repository.productmonitor.ProductMonitorRepository
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控数据页面的ViewModel
 * 管理监控数据展示页面的状态和业务逻辑
 */
class MonitoringDataViewModel(
    application: Application,
    private val monitoringPlanRepository: MonitoringPlanRepository,
    private val productMonitorRepository: ProductMonitorRepository
) : AndroidViewModel(application) {

    // 监控计划列表状态
    private val _monitoringPlans = MutableStateFlow<List<MonitoringPlanEntity>>(emptyList())
    val monitoringPlans: StateFlow<List<MonitoringPlanEntity>> = _monitoringPlans.asStateFlow()

    // 监控商品列表状态
    private val _monitoredProducts = MutableStateFlow<List<ProductMonitorEntity>>(emptyList())
    val monitoredProducts: StateFlow<List<ProductMonitorEntity>> = _monitoredProducts.asStateFlow()

    // 变化记录列表状态
    private val _changeRecords = MutableStateFlow<List<ProductChangeRecordEntity>>(emptyList())
    val changeRecords: StateFlow<List<ProductChangeRecordEntity>> = _changeRecords.asStateFlow()

    // 选中商品状态
    private val _selectedProduct = MutableStateFlow<ProductMonitorEntity?>(null)
    val selectedProduct: StateFlow<ProductMonitorEntity?> = _selectedProduct.asStateFlow()

    // 选中商品的变化记录状态
    private val _selectedProductChangeRecords = MutableStateFlow<List<ProductChangeRecordEntity>>(emptyList())
    val selectedProductChangeRecords: StateFlow<List<ProductChangeRecordEntity>> = _selectedProductChangeRecords.asStateFlow()

    // 加载状态
    private val _isLoading = mutableStateOf(true)
    val isLoading: State<Boolean> = _isLoading

    // 统计信息状态
    private val _statistics = MutableStateFlow<Map<String, Int>>(emptyMap())
    val statistics: StateFlow<Map<String, Int>> = _statistics.asStateFlow()

    // 错误信息状态
    private val _errorMessage = mutableStateOf<String?>(null)
    val errorMessage: State<String?> = _errorMessage

    // 日期格式化器
    val dateFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
    val fullDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

    init {
        initializeData()
    }

    /**
     * 初始化数据
     */
    private fun initializeData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null

                // 启动数据监听
                startDataObservation()
                
                // 加载统计信息
                loadStatistics()

            } catch (e: Exception) {
                Log.e("MonitoringDataViewModel", "Failed to initialize data", e)
                _errorMessage.value = "初始化数据失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 启动数据监听
     */
    private fun startDataObservation() {
        // 监听监控计划数据
        viewModelScope.launch {
            monitoringPlanRepository.getAllMonitoringPlans().collect { plans ->
                _monitoringPlans.value = plans
                Log.d("MonitoringDataViewModel", "Monitoring plans updated: ${plans.size}")
            }
        }

        // 监听监控商品数据
        viewModelScope.launch {
            productMonitorRepository.getAllProducts().collect { products ->
                _monitoredProducts.value = products
                Log.d("MonitoringDataViewModel", "Monitored products updated: ${products.size}")
            }
        }

        // 监听变化记录数据
        viewModelScope.launch {
            productMonitorRepository.getAllChangeRecords().collect { records ->
                _changeRecords.value = records.sortedByDescending { it.changeTime }
                Log.d("MonitoringDataViewModel", "Change records updated: ${records.size}")
            }
        }
    }

    /**
     * 加载统计信息
     */
    private suspend fun loadStatistics() {
        try {
            val stats = productMonitorRepository.getMonitoringStatistics()
            _statistics.value = stats
            Log.d("MonitoringDataViewModel", "Statistics loaded: $stats")
        } catch (e: Exception) {
            Log.e("MonitoringDataViewModel", "Failed to load statistics", e)
            _errorMessage.value = "加载统计信息失败: ${e.message}"
        }
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                // 重新加载统计信息
                loadStatistics()
                
                Log.d("MonitoringDataViewModel", "Data refreshed successfully")
            } catch (e: Exception) {
                Log.e("MonitoringDataViewModel", "Failed to refresh data", e)
                _errorMessage.value = "刷新数据失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 选择商品并加载其变化记录
     */
    fun selectProduct(product: ProductMonitorEntity?) {
        _selectedProduct.value = product
        if (product != null) {
            // 加载选中商品的变化记录
            viewModelScope.launch {
                productMonitorRepository.getProductChangeRecords(product.id).collect { records ->
                    _selectedProductChangeRecords.value = records.sortedByDescending { it.changeTime }
                    Log.d("MonitoringDataViewModel", "Product ${product.id} change records loaded: ${records.size}")
                }
            }
        } else {
            _selectedProductChangeRecords.value = emptyList()
        }
    }

    /**
     * 清除错误信息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * 获取监控概览统计数据
     */
    fun getOverviewStatistics(): Map<String, String> {
        val plans = _monitoringPlans.value
        val stats = _statistics.value
        
        return mapOf(
            "total_plans" to plans.size.toString(),
            "enabled_plans" to plans.count { it.isEnabled }.toString(),
            "total_products" to (stats["total_products"]?.toString() ?: "0"),
            "available_products" to (stats["available_products"]?.toString() ?: "0")
        )
    }

    /**
     * 获取执行统计信息
     */
    fun getExecutionStatistics(): Map<String, String> {
        val plans = _monitoringPlans.value
        val totalExecutions = plans.sumOf { it.executedCount }
        val avgExecutions = if (plans.isNotEmpty()) totalExecutions / plans.size else 0
        val maxExecutions = plans.maxOfOrNull { it.executedCount } ?: 0
        
        val recentlyExecuted = plans.count { plan ->
            plan.lastExecutedAt?.let { lastExecuted ->
                System.currentTimeMillis() - lastExecuted.time < 24 * 60 * 60 * 1000 // 24小时内
            } ?: false
        }
        
        return mapOf(
            "total_executions" to totalExecutions.toString(),
            "avg_executions" to avgExecutions.toString(),
            "max_executions" to maxExecutions.toString(),
            "recent_executions" to recentlyExecuted.toString()
        )
    }

    /**
     * ViewModel工厂
     */
    class Factory(
        private val application: Application,
        private val monitoringPlanRepository: MonitoringPlanRepository,
        private val productMonitorRepository: ProductMonitorRepository
    ) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(MonitoringDataViewModel::class.java)) {
                return MonitoringDataViewModel(
                    application,
                    monitoringPlanRepository,
                    productMonitorRepository
                ) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }

    companion object {
        private const val TAG = "MonitoringDataViewModel"
        
        /**
         * 创建ViewModel工厂的便捷方法
         */
        fun createFactory(application: Application): Factory {
            val monitoringPlanRepository = MonitoringServiceManager.getMonitoringPlanRepository(application)
            val productMonitorRepository = MonitoringServiceManager.getProductMonitorRepository(application)
            
            return Factory(application, monitoringPlanRepository, productMonitorRepository)
        }
    }
}
